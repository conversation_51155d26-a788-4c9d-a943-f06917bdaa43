<?php

namespace App\Livewire\Settings\Socials;

use Livewire\Component;

class WhatsappSettings extends Component
{
    public $isConnected = false;
    public $qrCode = '';
    public $sessionStatus = 'disconnected'; // disconnected, connecting, connected
    public $userInfo = [];
    public $connectionTime = null;
    public $isGeneratingQr = false;

    public function mount()
    {
        // For now, using dummy data - will be replaced with separate WhatsApp users table later
        // Simulate different connection states for testing
        $this->isConnected = false; // Change to true to test connected state
        $this->sessionStatus = 'disconnected'; // disconnected, connecting, connected

        // Dummy user data for when connected (for testing UI)
        if ($this->isConnected) {
            $this->userInfo = [
                'id' => '+<EMAIL>',
                'name' => '<PERSON>',
                'pushname' => '<PERSON>',
                'profilePicUrl' => 'https://via.placeholder.com/150/4CAF50/FFFFFF?text=JD',
                'platform' => 'android',
                'battery' => 85,
            ];
            $this->connectionTime = now()->subHours(2);
            $this->sessionStatus = 'connected';
        }
    }

    public function generateQrCode()
    {
        $this->isGeneratingQr = true;
        $this->sessionStatus = 'connecting';

        // This will be replaced with actual whatsapp-web.js integration
        // For now, we'll simulate QR code generation with a sample QR code
        $this->qrCode = 'https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=WhatsApp-Web-Demo-QR-Code';

        $this->isGeneratingQr = false;

        session()->flash('message', 'QR Code generated! Scan with your WhatsApp mobile app.');
    }

    public function simulateConnection()
    {
        // Method to simulate successful connection for testing
        $this->isConnected = true;
        $this->sessionStatus = 'connected';
        $this->qrCode = '';
        $this->userInfo = [
            'id' => '+<EMAIL>',
            'name' => 'John Doe',
            'pushname' => 'John Doe',
            'profilePicUrl' => 'https://via.placeholder.com/150/4CAF50/FFFFFF?text=JD',
            'platform' => 'android',
            'battery' => 85,
        ];
        $this->connectionTime = now();

        session()->flash('message', 'WhatsApp Web connected successfully!');
    }

    public function refreshQrCode()
    {
        $this->generateQrCode();
    }

    public function disconnect()
    {
        $this->isConnected = false;
        $this->sessionStatus = 'disconnected';
        $this->qrCode = '';
        $this->userInfo = [];
        $this->connectionTime = null;

        // TODO: Update WhatsApp users table when implemented
        // For now, just reset the component state

        session()->flash('message', 'WhatsApp Web session disconnected successfully!');
    }

    public function checkConnectionStatus()
    {
        // This method will be called by whatsapp-web.js to update connection status
        // For now, it's a placeholder for future integration
    }

    public function render()
    {
        return view('livewire.settings.socials.whatsapp-settings');
    }
}
