<?php

namespace App\Livewire\Settings\Socials;

use Livewire\Component;

class WhatsappSettings extends Component
{
    public $isConnected = false;
    public $qrCode = '';
    public $sessionStatus = 'disconnected'; // disconnected, connecting, connected
    public $userInfo = [];
    public $connectionTime = null;
    public $isGeneratingQr = false;

    public function mount()
    {
        // Load existing WhatsApp Web session data
        $user = auth()->user();
        if ($user) {
            $this->isConnected = $user->whatsapp_web_connected ?? false;
            $this->sessionStatus = $user->whatsapp_web_status ?? 'disconnected';
            $this->userInfo = json_decode($user->whatsapp_web_user_info ?? '[]', true);
            $this->connectionTime = $user->whatsapp_web_connected_at;
        }
    }

    public function generateQrCode()
    {
        $this->isGeneratingQr = true;
        $this->sessionStatus = 'connecting';

        // This will be replaced with actual whatsapp-web.js integration
        // For now, we'll simulate QR code generation
        $this->qrCode = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==';

        $this->isGeneratingQr = false;

        session()->flash('message', 'QR Code generated! Scan with your WhatsApp mobile app.');
    }

    public function refreshQrCode()
    {
        $this->generateQrCode();
    }

    public function disconnect()
    {
        $this->isConnected = false;
        $this->sessionStatus = 'disconnected';
        $this->qrCode = '';
        $this->userInfo = [];
        $this->connectionTime = null;

        // Update database
        $user = auth()->user();
        if ($user) {
            $user->update([
                'whatsapp_web_connected' => false,
                'whatsapp_web_status' => 'disconnected',
                'whatsapp_web_user_info' => null,
                'whatsapp_web_connected_at' => null,
            ]);
        }

        session()->flash('message', 'WhatsApp Web session disconnected successfully!');
    }

    public function checkConnectionStatus()
    {
        // This method will be called by whatsapp-web.js to update connection status
        // For now, it's a placeholder for future integration
    }

    public function render()
    {
        return view('livewire.settings.socials.whatsapp-settings');
    }
}
