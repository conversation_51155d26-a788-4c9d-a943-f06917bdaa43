<div class="space-y-6">
    <!-- Flash Messages -->
    @if (session()->has('message'))
        <div class="alert flex rounded-lg border border-success bg-success/10 py-4 px-4 text-success sm:px-5">
            <svg xmlns="http://www.w3.org/2000/svg" class="size-5" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
            </svg>
            <div class="ml-2">
                <h3 class="text-sm font-medium">{{ session('message') }}</h3>
            </div>
        </div>
    @endif

    <!-- Connection Status -->
    <div class="flex items-center justify-between p-4 border border-slate-200 rounded-lg dark:border-navy-500 {{ $isConnected ? 'bg-success/5 border-success/20' : 'bg-slate-50 dark:bg-navy-800' }}">
        <div class="flex items-center space-x-3">
            <div class="avatar size-10">
                <div class="is-initial rounded-full bg-green-500 text-white">
                    <i class="fab fa-whatsapp text-lg"></i>
                </div>
            </div>
            <div>
                <h4 class="font-medium">WhatsApp Web Connection</h4>
                <p class="text-sm {{ $isConnected ? 'text-success' : 'text-slate-400 dark:text-navy-300' }}">
                    @if($sessionStatus === 'connected')
                        Connected and Active
                    @elseif($sessionStatus === 'connecting')
                        Connecting...
                    @else
                        Not Connected
                    @endif
                </p>
            </div>
        </div>
        @if($isConnected)
            <div class="badge bg-success/10 text-success dark:bg-success/15">
                <div class="size-2 rounded-full bg-success mr-1"></div>
                Connected
            </div>
        @endif
    </div>

    @if(!$isConnected)
        <!-- QR Code Section -->
        <div class="space-y-4">
            <h4 class="font-medium text-slate-700 dark:text-navy-100">Connect WhatsApp Web</h4>

            <div class="bg-white dark:bg-navy-700 border border-slate-200 dark:border-navy-500 rounded-lg p-6">
                @if($sessionStatus === 'connecting' && $qrCode)
                    <div class="text-center space-y-4">
                        <div class="inline-block p-4 bg-white rounded-lg border-2 border-slate-200">
                            <img src="{{ $qrCode }}" alt="WhatsApp QR Code" class="w-48 h-48 mx-auto" />
                        </div>
                        <div class="space-y-2">
                            <p class="text-sm text-slate-600 dark:text-navy-300">
                                Scan this QR code with your WhatsApp mobile app
                            </p>
                            <p class="text-xs text-slate-500 dark:text-navy-400">
                                Open WhatsApp → Settings → Linked Devices → Link a Device
                            </p>
                        </div>
                        <div class="flex space-x-2">
                            <button wire:click="refreshQrCode"
                                class="btn border border-slate-300 text-slate-700 hover:bg-slate-150 focus:bg-slate-150 active:bg-slate-150/80 dark:border-navy-450 dark:text-navy-100 dark:hover:bg-navy-500 dark:focus:bg-navy-500 dark:active:bg-navy-500/90">
                                <i class="fas fa-sync-alt mr-2"></i>
                                Refresh QR Code
                            </button>
                            <!-- Temporary button for testing - remove when whatsapp-web.js is integrated -->
                            <button wire:click="simulateConnection"
                                class="btn bg-green-500 text-white hover:bg-green-600 focus:bg-green-600 active:bg-green-600/90">
                                <i class="fas fa-check mr-2"></i>
                                Simulate Connection
                            </button>
                        </div>
                    </div>
                @else
                    <div class="text-center space-y-4">
                        <div class="w-48 h-48 mx-auto bg-slate-100 dark:bg-navy-600 rounded-lg flex items-center justify-center">
                            <i class="fab fa-whatsapp text-6xl text-slate-400 dark:text-navy-300"></i>
                        </div>
                        <div class="space-y-2">
                            <h5 class="font-medium">Connect your WhatsApp</h5>
                            <p class="text-sm text-slate-600 dark:text-navy-300">
                                Generate a QR code to link your WhatsApp account
                            </p>
                        </div>
                        <button wire:click="generateQrCode"
                            wire:loading.attr="disabled"
                            wire:target="generateQrCode"
                            class="btn bg-green-500 font-medium text-white hover:bg-green-600 focus:bg-green-600 active:bg-green-600/90 disabled:opacity-50">
                            <span wire:loading.remove wire:target="generateQrCode">
                                <i class="fas fa-qrcode mr-2"></i>
                                Generate QR Code
                            </span>
                            <span wire:loading wire:target="generateQrCode">
                                <i class="fas fa-spinner fa-spin mr-2"></i>
                                Generating...
                            </span>
                        </button>
                    </div>
                @endif
            </div>
        </div>
    @else
        <!-- User Information Section -->
        <div class="space-y-4">
            <h4 class="font-medium text-slate-700 dark:text-navy-100">Connected Account</h4>

            <div class="bg-white dark:bg-navy-700 border border-slate-200 dark:border-navy-500 rounded-lg p-6">
                <div class="flex items-center space-x-4">
                    <div class="avatar size-16">
                        @if(isset($userInfo['profilePicUrl']) && $userInfo['profilePicUrl'])
                            <img src="{{ $userInfo['profilePicUrl'] }}" alt="Profile Picture" class="rounded-full" />
                        @else
                            <div class="is-initial rounded-full bg-green-500 text-white text-xl">
                                <i class="fas fa-user"></i>
                            </div>
                        @endif
                    </div>
                    <div class="flex-1">
                        <h5 class="font-medium text-lg">
                            {{ $userInfo['pushname'] ?? $userInfo['name'] ?? 'WhatsApp User' }}
                        </h5>
                        <p class="text-slate-600 dark:text-navy-300">
                            {{ $userInfo['id'] ?? 'Unknown Number' }}
                        </p>
                        @if($connectionTime)
                            <p class="text-sm text-slate-500 dark:text-navy-400">
                                Connected: {{ \Carbon\Carbon::parse($connectionTime)->diffForHumans() }}
                            </p>
                        @endif
                    </div>
                    <div class="text-right">
                        <div class="badge bg-success/10 text-success dark:bg-success/15 mb-2">
                            <div class="size-2 rounded-full bg-success mr-1"></div>
                            Active
                        </div>
                    </div>
                </div>

                @if(isset($userInfo['platform']))
                    <div class="mt-4 pt-4 border-t border-slate-200 dark:border-navy-500">
                        <div class="grid grid-cols-2 gap-4 text-sm">
                            <div>
                                <span class="text-slate-500 dark:text-navy-400">Platform:</span>
                                <span class="ml-2 font-medium">{{ ucfirst($userInfo['platform']) }}</span>
                            </div>
                            @if(isset($userInfo['battery']))
                                <div>
                                    <span class="text-slate-500 dark:text-navy-400">Battery:</span>
                                    <span class="ml-2 font-medium">{{ $userInfo['battery'] }}%</span>
                                </div>
                            @endif
                        </div>
                    </div>
                @endif
            </div>
        </div>
    @endif

    <!-- Action Buttons -->
    <div class="flex justify-between pt-4 border-t border-slate-200 dark:border-navy-500">
        <div class="flex space-x-2">
            @if($isConnected)
                <button wire:click="disconnect"
                    class="btn border border-error text-error hover:bg-error/10 focus:bg-error/10 active:bg-error/20">
                    <i class="fas fa-unlink mr-2"></i>
                    Disconnect
                </button>
            @endif
        </div>

        @if(!$isConnected && $sessionStatus !== 'connecting')
            <div class="flex space-x-2">
                <button wire:click="generateQrCode"
                    wire:loading.attr="disabled"
                    wire:target="generateQrCode"
                    class="btn bg-primary font-medium text-white hover:bg-primary-focus focus:bg-primary-focus active:bg-primary-focus/90 dark:bg-accent dark:hover:bg-accent-focus dark:focus:bg-accent-focus dark:active:bg-accent/90 disabled:opacity-50">
                    <span wire:loading.remove wire:target="generateQrCode">
                        <i class="fas fa-qrcode mr-2"></i>
                        Connect WhatsApp
                    </span>
                    <span wire:loading wire:target="generateQrCode">
                        <i class="fas fa-spinner fa-spin mr-2"></i>
                        Connecting...
                    </span>
                </button>
            </div>
        @endif
    </div>
</div>
