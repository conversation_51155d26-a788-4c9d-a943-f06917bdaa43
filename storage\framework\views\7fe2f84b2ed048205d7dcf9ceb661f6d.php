<!-- Modal Header Status (to be used in modal header) -->
<div class="flex items-center justify-between">
    <div class="flex items-center space-x-3">
        <div class="relative">
            <div class="avatar size-10 <?php echo e($isConnected ? 'ring-2 ring-green-100 dark:ring-green-900/50' : ''); ?>">
                <div class="is-initial rounded-full <?php echo e($isConnected ? 'bg-gradient-to-br from-green-500 to-emerald-600' : 'bg-gradient-to-br from-slate-400 to-gray-500'); ?> text-white">
                    <i class="fab fa-whatsapp text-lg"></i>
                </div>
            </div>
            <!--[if BLOCK]><![endif]--><?php if($isConnected): ?>
                <div class="absolute -bottom-0.5 -right-0.5 size-3 rounded-full bg-green-500 border border-white dark:border-navy-900"></div>
            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
        </div>
        <div>
            <h3 class="font-medium text-slate-800 dark:text-navy-100">WhatsApp Web</h3>
            <p class="text-xs <?php echo e($isConnected ? 'text-green-600 dark:text-green-400' : 'text-slate-500 dark:text-navy-300'); ?>">
                <!--[if BLOCK]><![endif]--><?php if($sessionStatus === 'connected'): ?>
                    <i class="fas fa-circle text-green-500 mr-1 animate-pulse"></i>
                    Connected and Active
                <?php elseif($sessionStatus === 'connecting'): ?>
                    <i class="fas fa-spinner fa-spin text-blue-500 mr-1"></i>
                    Connecting...
                <?php else: ?>
                    <i class="fas fa-circle text-slate-400 mr-1"></i>
                    Not Connected
                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
            </p>
        </div>
    </div>
    <!--[if BLOCK]><![endif]--><?php if($isConnected): ?>
        <div class="badge bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-300 px-2 py-1 rounded-full text-xs font-medium">
            <div class="size-1.5 rounded-full bg-green-500 mr-1 animate-pulse"></div>
            Online
        </div>
    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
</div>

<!-- Modal Body Content -->
<div class="space-y-4">

    <!--[if BLOCK]><![endif]--><?php if(!$isConnected): ?>
        <!-- QR Code Section -->
        <div class="space-y-4">
            <div class="bg-white dark:bg-navy-700 border border-slate-200 dark:border-navy-500 rounded-lg overflow-hidden">
                <!--[if BLOCK]><![endif]--><?php if($sessionStatus === 'connecting' && $qrCode): ?>
                    <div class="p-6">
                        <div class="text-center space-y-4">
                            <!-- QR Code Container -->
                            <div class="relative inline-block">
                                <div class="p-4 bg-white rounded-lg border-2 border-green-100 dark:border-green-900/50">
                                    <img src="<?php echo e($qrCode); ?>" alt="WhatsApp QR Code" class="w-40 h-40 mx-auto rounded" />
                                </div>
                                <div class="absolute -top-2 -right-2 size-6 bg-green-500 rounded-full flex items-center justify-center">
                                    <i class="fab fa-whatsapp text-white text-sm"></i>
                                </div>
                            </div>

                            <!-- Instructions -->
                            <div class="space-y-2 max-w-xs mx-auto">
                                <h5 class="text-sm font-medium text-slate-800 dark:text-navy-100">Scan with WhatsApp</h5>
                                <div class="space-y-1 text-xs text-slate-600 dark:text-navy-300">
                                    <div class="flex items-center justify-center space-x-2">
                                        <span class="flex items-center justify-center size-4 bg-green-100 dark:bg-green-900/30 text-green-600 dark:text-green-400 rounded-full text-xs font-bold">1</span>
                                        <span>Open WhatsApp on your phone</span>
                                    </div>
                                    <div class="flex items-center justify-center space-x-2">
                                        <span class="flex items-center justify-center size-4 bg-green-100 dark:bg-green-900/30 text-green-600 dark:text-green-400 rounded-full text-xs font-bold">2</span>
                                        <span>Settings → Linked Devices</span>
                                    </div>
                                    <div class="flex items-center justify-center space-x-2">
                                        <span class="flex items-center justify-center size-4 bg-green-100 dark:bg-green-900/30 text-green-600 dark:text-green-400 rounded-full text-xs font-bold">3</span>
                                        <span>Tap "Link a Device" and scan</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php else: ?>
                    <div class="p-6">
                        <div class="text-center space-y-4">
                            <!-- WhatsApp Icon -->
                            <div class="relative inline-block">
                                <div class="w-20 h-20 mx-auto bg-gradient-to-br from-green-100 to-emerald-100 dark:from-green-900/30 dark:to-emerald-900/30 rounded-2xl flex items-center justify-center">
                                    <i class="fab fa-whatsapp text-3xl text-green-500"></i>
                                </div>
                                <div class="absolute -bottom-1 -right-1 size-5 bg-slate-200 dark:bg-navy-600 rounded-full flex items-center justify-center">
                                    <i class="fas fa-qrcode text-slate-500 dark:text-navy-300 text-xs"></i>
                                </div>
                            </div>

                            <!-- Content -->
                            <div class="space-y-2 max-w-xs mx-auto">
                                <h5 class="text-sm font-medium text-slate-800 dark:text-navy-100">Ready to Connect</h5>
                                <p class="text-xs text-slate-600 dark:text-navy-300">
                                    Generate a QR code to link your WhatsApp account
                                </p>
                            </div>
                        </div>
                    </div>
                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
            </div>
        </div>
    <?php else: ?>
        <!-- Connected User Information -->
        <div class="space-y-4">
            <div class="bg-white dark:bg-navy-700 border border-slate-200 dark:border-navy-500 rounded-lg overflow-hidden">
                <div class="p-4">
                    <div class="flex items-center space-x-4">
                        <!-- Profile Picture -->
                        <div class="relative">
                            <div class="avatar size-12 ring-2 ring-green-100 dark:ring-green-900/50">
                                <!--[if BLOCK]><![endif]--><?php if(isset($userInfo['profilePicUrl']) && $userInfo['profilePicUrl']): ?>
                                    <img src="<?php echo e($userInfo['profilePicUrl']); ?>" alt="Profile Picture" class="rounded-full" />
                                <?php else: ?>
                                    <div class="is-initial rounded-full bg-gradient-to-br from-green-500 to-emerald-600 text-white">
                                        <i class="fas fa-user"></i>
                                    </div>
                                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                            </div>
                            <div class="absolute -bottom-0.5 -right-0.5 size-4 rounded-full bg-green-500 border border-white dark:border-navy-700 flex items-center justify-center">
                                <i class="fas fa-check text-white text-xs"></i>
                            </div>
                        </div>

                        <!-- User Details -->
                        <div class="flex-1">
                            <div class="space-y-1">
                                <h5 class="text-sm font-medium text-slate-800 dark:text-navy-100">
                                    <?php echo e($userInfo['pushname'] ?? $userInfo['name'] ?? 'WhatsApp User'); ?>

                                </h5>
                                <p class="text-xs text-slate-600 dark:text-navy-300">
                                    <?php echo e($userInfo['id'] ?? 'Unknown Number'); ?>

                                </p>
                                <!--[if BLOCK]><![endif]--><?php if($connectionTime): ?>
                                    <div class="flex items-center space-x-1 text-xs text-slate-500 dark:text-navy-400">
                                        <i class="fas fa-clock text-xs"></i>
                                        <span>Connected <?php echo e(\Carbon\Carbon::parse($connectionTime)->diffForHumans()); ?></span>
                                    </div>
                                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                            </div>
                        </div>

                        <!-- Status Badge -->
                        <div class="text-right">
                            <div class="inline-flex items-center px-2 py-1 bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-300 rounded-full text-xs font-medium">
                                <div class="size-1.5 rounded-full bg-green-500 mr-1 animate-pulse"></div>
                                Active
                            </div>
                        </div>
                    </div>

                    <!--[if BLOCK]><![endif]--><?php if(isset($userInfo['platform'])): ?>
                        <div class="mt-4 pt-4 border-t border-slate-200 dark:border-navy-500">
                            <div class="flex items-center justify-center space-x-6">
                                <div class="text-center">
                                    <div class="inline-flex items-center justify-center size-8 bg-slate-100 dark:bg-navy-600 rounded-lg mb-1">
                                        <i class="fas fa-mobile-alt text-slate-600 dark:text-navy-300 text-sm"></i>
                                    </div>
                                    <p class="text-xs text-slate-500 dark:text-navy-400 uppercase tracking-wide font-medium">Platform</p>
                                    <p class="text-xs font-medium text-slate-700 dark:text-navy-200"><?php echo e(ucfirst($userInfo['platform'])); ?></p>
                                </div>
                                <div class="text-center">
                                    <div class="inline-flex items-center justify-center size-8 bg-green-100 dark:bg-green-900/30 rounded-lg mb-1">
                                        <i class="fab fa-whatsapp text-green-600 dark:text-green-400 text-sm"></i>
                                    </div>
                                    <p class="text-xs text-slate-500 dark:text-navy-400 uppercase tracking-wide font-medium">Status</p>
                                    <p class="text-xs font-medium text-green-600 dark:text-green-400">Online</p>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                </div>
            </div>
        </div>
    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
</div>

<!-- Modal Footer Buttons -->
<div class="border-t border-slate-200 dark:border-navy-500 p-4">
    <!--[if BLOCK]><![endif]--><?php if($isConnected): ?>
        <div class="flex justify-center">
            <button wire:click="disconnect"
                class="btn border border-red-300 text-red-600 hover:bg-red-50 focus:bg-red-50 active:bg-red-100 dark:border-red-700 dark:text-red-400 dark:hover:bg-red-900/20 dark:focus:bg-red-900/20 dark:active:bg-red-900/30 rounded-lg px-6 py-2 text-sm font-medium transition-all duration-200">
                <i class="fas fa-unlink mr-2"></i>
                Disconnect WhatsApp
            </button>
        </div>
    <?php else: ?>
        <div class="flex justify-center space-x-3">
            <!--[if BLOCK]><![endif]--><?php if($sessionStatus === 'connecting' && $qrCode): ?>
                <button wire:click="refreshQrCode"
                    class="btn border border-slate-300 text-slate-700 hover:bg-slate-50 focus:bg-slate-50 active:bg-slate-100 dark:border-navy-450 dark:text-navy-100 dark:hover:bg-navy-600 dark:focus:bg-navy-600 dark:active:bg-navy-500 rounded-lg px-4 py-2 text-sm font-medium transition-all duration-200">
                    <i class="fas fa-sync-alt mr-2"></i>
                    Refresh Code
                </button>
                <!-- Temporary button for testing -->
                <button wire:click="simulateConnection"
                    class="btn bg-gradient-to-r from-green-500 to-emerald-600 text-white hover:from-green-600 hover:to-emerald-700 focus:from-green-600 focus:to-emerald-700 active:from-green-700 active:to-emerald-800 rounded-lg px-4 py-2 text-sm font-medium transition-all duration-200">
                    <i class="fas fa-check mr-2"></i>
                    Test Connection
                </button>
            <?php else: ?>
                <button wire:click="generateQrCode"
                    wire:loading.attr="disabled"
                    wire:target="generateQrCode"
                    class="btn bg-gradient-to-r from-green-500 to-emerald-600 text-white hover:from-green-600 hover:to-emerald-700 focus:from-green-600 focus:to-emerald-700 active:from-green-700 active:to-emerald-800 disabled:opacity-50 disabled:cursor-not-allowed rounded-lg px-6 py-2 text-sm font-medium transition-all duration-200">
                    <span wire:loading.remove wire:target="generateQrCode">
                        <i class="fas fa-qrcode mr-2"></i>
                        Generate QR Code
                    </span>
                    <span wire:loading wire:target="generateQrCode">
                        <i class="fas fa-spinner fa-spin mr-2"></i>
                        Generating...
                    </span>
                </button>
            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
        </div>
    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
</div>
<?php /**PATH D:\Laravel_Projects\alhars\resources\views/livewire/settings/socials/whatsapp-settings.blade.php ENDPATH**/ ?>