<?php

namespace App\Livewire\Settings\Socials;

use Livewire\Component;

class InstagramSettings extends Component
{
    public $isConnected = false;
    public $sessionStatus = 'disconnected'; // disconnected, connecting, connected
    public $userInfo = [];
    public $connectionTime = null;
    public $isConnecting = false;

    public function mount()
    {
        // For now, using dummy data - will be replaced with separate Instagram users table later
        // Simulate different connection states for testing
        $this->isConnected = false; // Change to true to test connected state
        $this->sessionStatus = 'disconnected'; // disconnected, connecting, connected

        // Dummy user data for when connected (for testing UI)
        if ($this->isConnected) {
            $this->userInfo = [
                'id' => 'john_doe_official',
                'username' => 'john_doe_official',
                'full_name' => 'John Do<PERSON>',
                'profile_pic_url' => 'https://via.placeholder.com/150/E4405F/FFFFFF?text=JD',
                'followers_count' => 12500,
                'following_count' => 850,
            ];
            $this->connectionTime = now()->subHours(1);
            $this->sessionStatus = 'connected';
        }
    }

    public function connectInstagram()
    {
        $this->isConnecting = true;
        $this->sessionStatus = 'connecting';

        // This will be replaced with actual Instagram API integration
        // For now, we'll simulate connection process

        $this->isConnecting = false;

        $this->dispatch('toastify', [
            'type' => 'success',
            'message' => 'Redirecting to Instagram for authorization...'
        ]);
    }

    public function simulateConnection()
    {
        // Method to simulate successful connection for testing
        $this->isConnected = true;
        $this->sessionStatus = 'connected';
        $this->userInfo = [
            'id' => 'john_doe_official',
            'username' => 'john_doe_official',
            'full_name' => 'John Doe',
            'profile_pic_url' => 'https://via.placeholder.com/150/E4405F/FFFFFF?text=JD',
            'followers_count' => 12500,
            'following_count' => 850,
        ];
        $this->connectionTime = now();

        $this->dispatch('toastify', [
            'type' => 'success',
            'message' => 'Instagram account connected successfully!'
        ]);
    }

    public function disconnect()
    {
        $this->isConnected = false;
        $this->sessionStatus = 'disconnected';
        $this->userInfo = [];
        $this->connectionTime = null;

        // TODO: Update Instagram users table when implemented
        // For now, just reset the component state

        $this->dispatch('toastify', [
            'type' => 'success',
            'message' => 'Instagram account disconnected successfully!'
        ]);
    }

    public function render()
    {
        return view('livewire.settings.socials.instagram-settings');
    }
}
